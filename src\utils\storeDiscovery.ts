// Store product discovery utilities for Shopify stores

interface DiscoveredProduct {
  handle: string;
  title: string;
  price: string;
  compare_at_price?: string;
  vendor: string;
  type: string;
  url: string;
  image?: string;
  available: boolean;
  currency?: string;
  currency_symbol?: string;
}

interface ShopifyProduct {
  id: number;
  title: string;
  handle: string;
  vendor: string;
  product_type: string;
  created_at: string;
  updated_at: string;
  published_at: string;
  template_suffix: string;
  status: string;
  published_scope: string;
  tags: string;
  admin_graphql_api_id: string;
  variants: Array<{
    id: number;
    product_id: number;
    title: string;
    price: string;
    sku: string;
    position: number;
    inventory_policy: string;
    compare_at_price: string | null;
    fulfillment_service: string;
    inventory_management: string;
    option1: string;
    option2: string | null;
    option3: string | null;
    created_at: string;
    updated_at: string;
    taxable: boolean;
    barcode: string;
    grams: number;
    image_id: number | null;
    weight: number;
    weight_unit: string;
    inventory_item_id: number;
    inventory_quantity: number;
    old_inventory_quantity: number;
    requires_shipping: boolean;
    admin_graphql_api_id: string;
  }>;
  options: Array<{
    id: number;
    product_id: number;
    name: string;
    position: number;
    values: string[];
  }>;
  images: Array<{
    id: number;
    product_id: number;
    position: number;
    created_at: string;
    updated_at: string;
    alt: string | null;
    width: number;
    height: number;
    src: string;
    variant_ids: number[];
    admin_graphql_api_id: string;
  }>;
  image: {
    id: number;
    product_id: number;
    position: number;
    created_at: string;
    updated_at: string;
    alt: string | null;
    width: number;
    height: number;
    src: string;
    variant_ids: number[];
    admin_graphql_api_id: string;
  } | null;
}

const CORS_PROXY = 'https://api.allorigins.win/raw?url=';

/**
 * Get currency symbol from currency code
 */
function getCurrencySymbol(currencyCode: string): string {
  const currencySymbols: { [key: string]: string } = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'CAD': 'C$',
    'AUD': 'A$',
    'CHF': 'CHF',
    'CNY': '¥',
    'SEK': 'kr',
    'NOK': 'kr',
    'DKK': 'kr',
    'PLN': 'zł',
    'CZK': 'Kč',
    'HUF': 'Ft',
    'RUB': '₽',
    'BRL': 'R$',
    'INR': '₹',
    'KRW': '₩',
    'SGD': 'S$',
    'HKD': 'HK$',
    'NZD': 'NZ$',
    'MXN': '$',
    'ZAR': 'R',
    'TRY': '₺',
    'ILS': '₪'
  };

  return currencySymbols[currencyCode.toUpperCase()] || currencyCode;
}

/**
 * Normalize a Shopify store URL to ensure it's in the correct format
 */
function normalizeStoreUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    let hostname = urlObj.hostname;
    
    // Handle different Shopify URL formats
    if (hostname.includes('.myshopify.com')) {
      // Already in myshopify.com format
      return `https://${hostname}`;
    } else if (hostname.includes('.shopify.com')) {
      // Convert from other shopify.com formats
      const storeName = hostname.split('.')[0];
      return `https://${storeName}.myshopify.com`;
    } else {
      // Assume it's a custom domain, try to find the myshopify.com equivalent
      // This is more complex and might require additional API calls
      return `https://${hostname}`;
    }
  } catch (error) {
    throw new Error('Invalid URL format');
  }
}

/**
 * Discover products from a Shopify store using multiple methods
 */
export async function discoverStoreProducts(storeUrl: string): Promise<DiscoveredProduct[]> {
  console.log('🔍 Starting store product discovery for:', storeUrl);
  
  const normalizedUrl = normalizeStoreUrl(storeUrl);
  console.log('📍 Normalized store URL:', normalizedUrl);
  
  const discoveredProducts: DiscoveredProduct[] = [];
  
  try {
    // Method 1: Try to get products from the products.json endpoint
    const productsFromJson = await discoverFromProductsJson(normalizedUrl);
    discoveredProducts.push(...productsFromJson);
    
    if (discoveredProducts.length > 0) {
      console.log(`✅ Found ${discoveredProducts.length} products via products.json`);
      return discoveredProducts;
    }
  } catch (error) {
    console.warn('❌ products.json method failed:', error);
  }
  
  try {
    // Method 2: Try to get products from collections
    const productsFromCollections = await discoverFromCollections(normalizedUrl);
    discoveredProducts.push(...productsFromCollections);
    
    if (discoveredProducts.length > 0) {
      console.log(`✅ Found ${discoveredProducts.length} products via collections`);
      return discoveredProducts;
    }
  } catch (error) {
    console.warn('❌ collections method failed:', error);
  }
  
  try {
    // Method 3: Try to get products from sitemap
    const productsFromSitemap = await discoverFromSitemap(normalizedUrl);
    discoveredProducts.push(...productsFromSitemap);
    
    if (discoveredProducts.length > 0) {
      console.log(`✅ Found ${discoveredProducts.length} products via sitemap`);
      return discoveredProducts;
    }
  } catch (error) {
    console.warn('❌ sitemap method failed:', error);
  }
  
  throw new Error('Could not discover products from the store using any available method');
}

/**
 * Discover products using the products.json endpoint
 */
async function discoverFromProductsJson(storeUrl: string): Promise<DiscoveredProduct[]> {
  console.log('🔍 Trying products.json endpoint...');
  
  const products: DiscoveredProduct[] = [];
  let page = 1;
  const limit = 250; // Maximum allowed by Shopify
  
  while (true) {
    const endpoint = `${storeUrl}/products.json?limit=${limit}&page=${page}`;
    console.log(`📄 Fetching page ${page}: ${endpoint}`);
    
    try {
      // Try direct fetch first, then CORS proxy
      let response: Response;
      try {
        response = await fetch(endpoint);
      } catch (corsError) {
        console.log('🔄 Direct fetch failed, trying CORS proxy...');
        response = await fetch(`${CORS_PROXY}${encodeURIComponent(endpoint)}`);
      }
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (!data.products || data.products.length === 0) {
        console.log(`📄 Page ${page} returned no products, stopping pagination`);
        break;
      }
      
      console.log(`📄 Page ${page} returned ${data.products.length} products`);
      
      // Convert Shopify products to our format
      const pageProducts = data.products.map((product: ShopifyProduct) => convertShopifyProduct(product, storeUrl));
      products.push(...pageProducts);
      
      // If we got fewer products than the limit, we've reached the end
      if (data.products.length < limit) {
        break;
      }
      
      page++;
      
      // Add a small delay to be respectful to the server
      await new Promise(resolve => setTimeout(resolve, 100));
      
    } catch (error) {
      console.error(`❌ Failed to fetch page ${page}:`, error);
      if (page === 1) {
        throw error; // If first page fails, throw error
      } else {
        break; // If later pages fail, just stop pagination
      }
    }
  }
  
  return products;
}

/**
 * Discover products from collections endpoint
 */
async function discoverFromCollections(storeUrl: string): Promise<DiscoveredProduct[]> {
  console.log('🔍 Trying collections method...');
  
  // This is a simplified implementation
  // In a real scenario, you'd first get all collections, then get products from each collection
  const products: DiscoveredProduct[] = [];
  
  try {
    const collectionsEndpoint = `${storeUrl}/collections.json`;
    let response: Response;
    
    try {
      response = await fetch(collectionsEndpoint);
    } catch (corsError) {
      response = await fetch(`${CORS_PROXY}${encodeURIComponent(collectionsEndpoint)}`);
    }
    
    if (response.ok) {
      const data = await response.json();
      console.log(`📦 Found ${data.collections?.length || 0} collections`);
      
      // For now, just try the "all" collection which often exists
      const allCollectionEndpoint = `${storeUrl}/collections/all/products.json`;
      try {
        let allResponse: Response;
        try {
          allResponse = await fetch(allCollectionEndpoint);
        } catch (corsError) {
          allResponse = await fetch(`${CORS_PROXY}${encodeURIComponent(allCollectionEndpoint)}`);
        }
        
        if (allResponse.ok) {
          const allData = await allResponse.json();
          if (allData.products) {
            const collectionProducts = allData.products.map((product: ShopifyProduct) => 
              convertShopifyProduct(product, storeUrl)
            );
            products.push(...collectionProducts);
          }
        }
      } catch (error) {
        console.warn('❌ Failed to fetch from "all" collection:', error);
      }
    }
  } catch (error) {
    console.error('❌ Collections discovery failed:', error);
    throw error;
  }
  
  return products;
}

/**
 * Discover products from sitemap.xml
 */
async function discoverFromSitemap(storeUrl: string): Promise<DiscoveredProduct[]> {
  console.log('🔍 Trying sitemap method...');
  
  const products: DiscoveredProduct[] = [];
  
  try {
    const sitemapUrl = `${storeUrl}/sitemap.xml`;
    let response: Response;
    
    try {
      response = await fetch(sitemapUrl);
    } catch (corsError) {
      response = await fetch(`${CORS_PROXY}${encodeURIComponent(sitemapUrl)}`);
    }
    
    if (response.ok) {
      const sitemapXml = await response.text();
      
      // Parse sitemap to find product URLs
      const productUrlRegex = /<loc>(.*?\/products\/[^<]+)<\/loc>/g;
      const productUrls: string[] = [];
      let match;
      
      while ((match = productUrlRegex.exec(sitemapXml)) !== null) {
        productUrls.push(match[1]);
      }
      
      console.log(`🗺️ Found ${productUrls.length} product URLs in sitemap`);
      
      // For each product URL, extract the handle and create a basic product entry
      for (const productUrl of productUrls.slice(0, 50)) { // Limit to first 50 for performance
        try {
          const urlParts = new URL(productUrl);
          const pathParts = urlParts.pathname.split('/');
          const handle = pathParts[pathParts.length - 1];
          
          if (handle) {
            products.push({
              handle,
              title: handle.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
              price: '0.00',
              vendor: 'Unknown',
              type: 'Unknown',
              url: productUrl,
              available: true
            });
          }
        } catch (error) {
          console.warn('❌ Failed to parse product URL:', productUrl, error);
        }
      }
    }
  } catch (error) {
    console.error('❌ Sitemap discovery failed:', error);
    throw error;
  }
  
  return products;
}

/**
 * Convert a Shopify API product to our DiscoveredProduct format
 */
function convertShopifyProduct(product: ShopifyProduct, storeUrl: string): DiscoveredProduct {
  const firstVariant = product.variants?.[0];
  const price = firstVariant?.price || '0.00';
  const compareAtPrice = firstVariant?.compare_at_price || undefined;
  const image = product.image?.src || product.images?.[0]?.src;

  // Try to detect currency from the store URL or use USD as default
  // In a real implementation, you might want to make an additional API call to get store info
  const currency = 'USD'; // Default, could be enhanced to detect from store
  const currencySymbol = getCurrencySymbol(currency);

  return {
    handle: product.handle,
    title: product.title,
    price: price,
    compare_at_price: compareAtPrice,
    vendor: product.vendor || 'Unknown',
    type: product.product_type || 'Unknown',
    url: `${storeUrl}/products/${product.handle}`,
    image: image,
    available: product.status === 'active' && (firstVariant?.inventory_quantity || 0) > 0,
    currency: currency,
    currency_symbol: currencySymbol
  };
}
