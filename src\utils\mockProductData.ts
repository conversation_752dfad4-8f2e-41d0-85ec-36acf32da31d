// Mock data generator for demo purposes
// This simulates what the backend would return

interface ProductVariant {
  id: string;
  title: string;
  price: string;
  sku?: string;
  inventory_quantity?: number;
  weight?: number;
  option1?: string;
  option2?: string;
  option3?: string;
}

interface ProductData {
  handle: string;
  title: string;
  body_html: string;
  vendor: string;
  type: string;
  tags: string[];
  published: boolean;
  images: string[];
  variants: ProductVariant[];
  price: string;
  compare_at_price?: string;
}

// Check if we're in development mode and can use CORS proxy
// Force CORS proxy usage in browser environment since direct fetches will fail due to CORS
const isDevelopment = true; // Always use CORS proxy in browser
const CORS_PROXY = 'https://api.allorigins.win/raw?url=';

console.log('🔧 Environment setup - forcing CORS proxy usage in browser');

// Debug environment detection
console.log('🔧 Environment check:', {
  'import.meta.env.DEV': import.meta.env.DEV,
  'import.meta.env.MODE': import.meta.env.MODE,
  'import.meta.env.PROD': import.meta.env.PROD,
  isDevelopment: isDevelopment
});

export async function generateMockProductData(url: string): Promise<ProductData> {
  console.log('🚀 FUNCTION CALLED - generateMockProductData with URL:', url);

  try {
    // Extract product handle from URL
    const urlParts = new URL(url);
    const pathParts = urlParts.pathname.split('/');
    const handle = pathParts[pathParts.indexOf('products') + 1] || 'sample-product';

    console.log('🚀 Starting product data extraction for:', url);
    console.log('🔍 URL parts:', {
      origin: urlParts.origin,
      pathname: urlParts.pathname,
      pathParts: pathParts
    });
    console.log('📝 Extracted handle:', handle);
    console.log('🛠️ Development mode:', isDevelopment);
    console.log('🌐 CORS proxy URL:', CORS_PROXY);

    // Try multiple methods to get product data
    let productData = null;

    // Method 1: Try Shopify product JSON endpoint
    try {
      const productUrl = `${urlParts.origin}/products/${handle}.js`;
      console.log('Attempting to fetch from JSON endpoint:', productUrl);

      // In development, try CORS proxy first, then direct fetch (CORS proxy has better success rate)
      const fetchUrls = isDevelopment
        ? [`${CORS_PROXY}${encodeURIComponent(productUrl)}`, productUrl]
        : [productUrl];

      console.log('🎯 Fetch URLs to try:', fetchUrls);
      console.log('🔗 Product URL:', productUrl);
      console.log('🌐 CORS Proxy URL:', isDevelopment ? `${CORS_PROXY}${encodeURIComponent(productUrl)}` : 'Not using proxy');

      let response = null;
      let lastError = null;

      for (const fetchUrl of fetchUrls) {
        try {
          const method = fetchUrl === productUrl ? 'direct' : 'CORS proxy';
          console.log(`🔄 Trying ${method} fetch from:`, fetchUrl);

          response = await fetch(fetchUrl, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
          });

          console.log(`📊 ${method} response:`, {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok,
            headers: Object.fromEntries(response.headers.entries())
          });

          if (response.ok) {
            const responseText = await response.text();
            console.log(`📄 ${method} response text length:`, responseText.length);
            console.log(`📄 ${method} response preview:`, responseText.substring(0, 200));

            try {
              productData = JSON.parse(responseText);
              console.log(`✅ ${method} fetch successful! Product data:`, {
                title: productData.title,
                price: productData.price,
                variants: productData.variants?.length,
                images: productData.images?.length,
                handle: productData.handle
              });
              break; // Success, exit the loop
            } catch (parseError) {
              console.error(`❌ ${method} JSON parse failed:`, parseError.message);
              throw new Error(`${method} JSON parse failed: ${parseError.message}`);
            }
          } else {
            const errorText = await response.text();
            console.error(`❌ ${method} error response:`, errorText.substring(0, 500));
            throw new Error(`${method} fetch failed: ${response.status} - ${response.statusText}`);
          }
        } catch (fetchError) {
          const method = fetchUrl === productUrl ? 'direct' : 'CORS proxy';
          console.warn(`❌ ${method} fetch failed:`, {
            message: fetchError.message,
            name: fetchError.name,
            stack: fetchError.stack?.substring(0, 300)
          });
          lastError = fetchError;
          continue; // Try next URL
        }
      }

      if (!productData && lastError) {
        throw lastError;
      }

    } catch (jsonError) {
      console.warn('All JSON endpoint methods failed:', jsonError.message);
      // CORS errors are common when fetching from external domains
      if (jsonError.message.includes('CORS') || jsonError.message.includes('fetch')) {
        console.log('CORS error detected, will try HTML parsing method');
      }
    }

    // Method 2: If JSON endpoint failed, try to extract from HTML page
    if (!productData) {
      console.log('Attempting to extract data from HTML page...');

      // In development, try CORS proxy first, then direct fetch (CORS proxy has better success rate)
      const fetchUrls = isDevelopment
        ? [`${CORS_PROXY}${encodeURIComponent(url)}`, url]
        : [url];

      console.log('🎯 HTML Fetch URLs to try:', fetchUrls);
      console.log('🔗 Original URL:', url);
      console.log('🌐 HTML CORS Proxy URL:', isDevelopment ? `${CORS_PROXY}${encodeURIComponent(url)}` : 'Not using proxy');

      let htmlResponse = null;
      let lastError = null;

      for (const fetchUrl of fetchUrls) {
        try {
          const method = fetchUrl === url ? 'direct' : 'CORS proxy';
          console.log(`Trying HTML ${method} fetch from:`, fetchUrl);

          htmlResponse = await fetch(fetchUrl, {
            method: 'GET',
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
          });

          if (htmlResponse.ok) {
            const html = await htmlResponse.text();
            console.log(`✅ HTML ${method} fetch successful! Length:`, html.length);
            productData = extractProductDataFromHTML(html, urlParts, handle);
            if (productData) {
              console.log(`✅ Product data extracted from HTML via ${method}:`, {
                title: productData.title,
                price: productData.price,
                handle: productData.handle
              });
            }
            break; // Success, exit the loop
          } else {
            throw new Error(`HTML ${method} fetch failed: ${htmlResponse.status}`);
          }
        } catch (fetchError) {
          const method = fetchUrl === url ? 'direct' : 'CORS proxy';
          console.warn(`❌ HTML ${method} fetch failed:`, fetchError.message);
          lastError = fetchError;
          continue; // Try next URL
        }
      }

      if (!productData && lastError) {
        // If both JSON and HTML methods fail due to CORS, we need to inform the user
        throw new Error(`Unable to fetch data from ${urlParts.origin}. This may be due to CORS policy restrictions when accessing external websites from the browser. In production, this would be handled server-side.`);
      }
    }

    if (productData) {
      console.log('Processing extracted product data...', {
        hasTitle: !!productData.title,
        hasPrice: !!productData.price,
        hasVariants: !!productData.variants,
        hasImages: !!productData.images,
        variantCount: productData.variants?.length,
        imageCount: productData.images?.length
      });

      // Extract and format the data from Shopify's product JSON
      const variants = productData.variants?.map((variant: any, index: number) => ({
        id: variant.id?.toString() || `var-${index}`,
        title: variant.title || 'Default Title',
        price: typeof variant.price === 'number' ? (variant.price / 100).toFixed(2) :
               typeof variant.price === 'string' ? variant.price : '0.00',
        sku: variant.sku || '',
        inventory_quantity: variant.inventory_quantity || 0,
        weight: variant.weight || 0,
        option1: variant.option1 || null,
        option2: variant.option2 || null,
        option3: variant.option3 || null
      })) || [];

      console.log('Processed variants:', variants.length, variants.slice(0, 2));

      // Process images with better URL handling
      const images = productData.images?.map((img: string) => {
        let imageUrl = img;

        // Handle different URL formats
        if (imageUrl.startsWith('//')) {
          imageUrl = `https:${imageUrl}`;
        } else if (imageUrl.startsWith('/')) {
          imageUrl = `${urlParts.origin}${imageUrl}`;
        }

        // Remove size parameters to get high-quality images
        imageUrl = imageUrl.replace(/(_\d+x\d*|_\d*x\d+|_compact|_grande|_large|_medium|_small|_thumb)\.(jpg|jpeg|png|gif|webp)/i, '.$2');

        return imageUrl;
      }) || [];

      // Process price
      const mainPrice = typeof productData.price === 'number' ?
        (productData.price / 100).toFixed(2) :
        variants.length > 0 ? variants[0].price : '0.00';

      const compareAtPrice = productData.compare_at_price && typeof productData.compare_at_price === 'number' ?
        (productData.compare_at_price / 100).toFixed(2) : undefined;

      const result = {
        handle: productData.handle || handle,
        title: productData.title || `Product ${handle}`,
        body_html: productData.description || '<p>No description available.</p>',
        vendor: productData.vendor || 'Unknown Vendor',
        type: productData.type || 'General',
        tags: Array.isArray(productData.tags) ? productData.tags : [],
        published: productData.available !== false,
        images: images.filter(img => img && img.length > 0),
        variants: variants.length > 0 ? variants : [{
          id: 'default-var',
          title: 'Default Title',
          price: mainPrice,
          sku: handle.toUpperCase(),
          inventory_quantity: 1,
          weight: 0
        }],
        price: mainPrice,
        compare_at_price: compareAtPrice
      };

      console.log('Final processed result:', {
        title: result.title,
        price: result.price,
        vendor: result.vendor,
        imageCount: result.images.length,
        variantCount: result.variants.length
      });

      return result;
    }

    // If no product data was extracted, throw an error to trigger fallback
    console.log('No product data was extracted, throwing error');
    throw new Error('No product data could be extracted from any method');

  } catch (error) {
    console.error('💥 CRITICAL ERROR - Falling back to error data:', error);
    console.error('🔍 Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      name: error instanceof Error ? error.name : 'Unknown',
      stack: error instanceof Error ? error.stack : undefined,
      url: url
    });

    // Final fallback with extracted handle
    const urlParts = new URL(url);
    const pathParts = urlParts.pathname.split('/');
    const handle = pathParts[pathParts.indexOf('products') + 1] || 'sample-product';

    console.log('⚠️ Using fallback error data for handle:', handle);
    console.log('🚨 THIS MEANS BOTH JSON AND HTML METHODS FAILED!');

    return {
      handle: handle,
      title: `Product ${handle}`,
      body_html: '<p>Unable to fetch product data. Please check the URL and try again.</p>',
      vendor: 'Unknown Vendor',
      type: 'General',
      tags: ['error'],
      published: true,
      images: ['https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=800&h=800&fit=crop'],
      variants: [{
        id: 'error-var',
        title: 'Default Title',
        price: '0.00',
        sku: 'ERROR-001',
        inventory_quantity: 0,
        weight: 0
      }],
      price: '0.00',
      compare_at_price: undefined
    };
  }
}

// Extract product data from HTML when JSON endpoint is not available
function extractProductDataFromHTML(html: string, urlParts: URL, handle: string): any {
  console.log('Extracting product data from HTML...');

  // Method 1: Look for Shopify product JSON in script tags
  const productJsonRegex = /"product":\s*({[^}]+})/;
  const productJsonMatch = html.match(productJsonRegex);

  if (productJsonMatch) {
    try {
      const productJson = JSON.parse(productJsonMatch[1]);
      console.log('Found product JSON in script tag');
      return productJson;
    } catch (e) {
      console.warn('Failed to parse product JSON from script tag');
    }
  }

  // Method 2: Look for window.ShopifyAnalytics or similar global objects
  const shopifyDataRegex = /window\.ShopifyAnalytics[^}]+product[^}]+({[^}]+})/;
  const shopifyDataMatch = html.match(shopifyDataRegex);

  if (shopifyDataMatch) {
    try {
      const shopifyData = JSON.parse(shopifyDataMatch[1]);
      console.log('Found product data in Shopify analytics');
      return shopifyData;
    } catch (e) {
      console.warn('Failed to parse Shopify analytics data');
    }
  }

  // Method 3: Look for JSON-LD structured data
  const jsonLdRegex = /<script[^>]*type=["']application\/ld\+json["'][^>]*>(.*?)<\/script>/gis;
  let jsonLdMatch;

  while ((jsonLdMatch = jsonLdRegex.exec(html)) !== null) {
    try {
      const structuredData = JSON.parse(jsonLdMatch[1]);
      if (structuredData['@type'] === 'Product' ||
          (Array.isArray(structuredData) && structuredData.some(item => item['@type'] === 'Product'))) {
        console.log('Found product data in JSON-LD');
        const productData = Array.isArray(structuredData)
          ? structuredData.find(item => item['@type'] === 'Product')
          : structuredData;

        // Convert JSON-LD to Shopify format
        return convertJsonLdToShopifyFormat(productData, handle);
      }
    } catch (e) {
      console.warn('Failed to parse JSON-LD data');
    }
  }

  // Method 4: Manual HTML parsing as last resort
  console.log('Falling back to manual HTML parsing...');
  return parseProductFromHTML(html, urlParts, handle);
}

// Convert JSON-LD structured data to Shopify product format
function convertJsonLdToShopifyFormat(jsonLd: any, handle: string): any {
  const offers = Array.isArray(jsonLd.offers) ? jsonLd.offers[0] : jsonLd.offers;

  return {
    handle: handle,
    title: jsonLd.name || '',
    description: jsonLd.description || '',
    vendor: jsonLd.brand?.name || '',
    type: jsonLd.category || '',
    tags: jsonLd.keywords ? (Array.isArray(jsonLd.keywords) ? jsonLd.keywords : [jsonLd.keywords]) : [],
    available: offers?.availability === 'http://schema.org/InStock' || offers?.availability === 'InStock',
    images: Array.isArray(jsonLd.image) ? jsonLd.image : (jsonLd.image ? [jsonLd.image] : []),
    price: offers?.price ? Math.round(parseFloat(offers.price) * 100) : 0, // Convert to cents
    compare_at_price: offers?.priceValidUntil ? Math.round(parseFloat(offers.price) * 100) : null,
    variants: [{
      id: Date.now(),
      title: 'Default Title',
      price: offers?.price ? Math.round(parseFloat(offers.price) * 100) : 0,
      available: offers?.availability === 'http://schema.org/InStock' || offers?.availability === 'InStock',
      sku: jsonLd.sku || '',
      inventory_quantity: 1,
      weight: 0
    }]
  };
}

// Manual HTML parsing as last resort
function parseProductFromHTML(html: string, urlParts: URL, handle: string): any {
  console.log('Parsing product data manually from HTML...');

  // Extract title
  const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
  const title = titleMatch ? titleMatch[1].replace(/\s*\|\s*.*$/g, '').trim() : `Product ${handle}`;

  // Extract description from meta description or first paragraph
  let description = '';
  const metaDescMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)["']/i);
  if (metaDescMatch) {
    description = metaDescMatch[1];
  } else {
    const firstParagraphMatch = html.match(/<p[^>]*>(.*?)<\/p>/i);
    if (firstParagraphMatch) {
      description = firstParagraphMatch[1].replace(/<[^>]*>/g, '');
    }
  }

  // Extract price - look for various price patterns
  let price = 0;
  const pricePatterns = [
    /["']price["']\s*:\s*["']?(\d+\.?\d*)["']?/i,
    /["']amount["']\s*:\s*["']?(\d+\.?\d*)["']?/i,
    /\$(\d+\.?\d*)/,
    /price[^>]*>.*?\$?(\d+\.?\d*)/i,
    /(\d+\.?\d*)\s*USD/i
  ];

  for (const pattern of pricePatterns) {
    const match = html.match(pattern);
    if (match) {
      const priceValue = parseFloat(match[1]);
      // If price is less than 10, assume it's in dollars, otherwise in cents
      price = priceValue < 10 ? Math.round(priceValue * 100) : Math.round(priceValue);
      break;
    }
  }

  // Extract images - look for product images specifically
  const images: string[] = [];
  const imagePatterns = [
    /<img[^>]+src=["']([^"']*product[^"']*)["'][^>]*>/gi,
    /<img[^>]+src=["']([^"']*\/files\/[^"']*)["'][^>]*>/gi,
    /<img[^>]+src=["']([^"']*cdn\.shopify[^"']*)["'][^>]*>/gi,
    /<img[^>]+data-src=["']([^"']*product[^"']*)["'][^>]*>/gi
  ];

  for (const pattern of imagePatterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      let imgSrc = match[1];

      // Clean up and normalize image URLs
      if (imgSrc.startsWith('//')) {
        imgSrc = `https:${imgSrc}`;
      } else if (imgSrc.startsWith('/')) {
        imgSrc = `${urlParts.origin}${imgSrc}`;
      }

      // Remove size parameters to get original image
      imgSrc = imgSrc.replace(/(_\d+x\d*|_\d*x\d+|_compact|_grande|_large|_medium|_small|_thumb)\.(jpg|jpeg|png|gif|webp)/i, '.$2');

      if (!images.includes(imgSrc) && imgSrc.match(/\.(jpg|jpeg|png|gif|webp)(\?|$)/i)) {
        images.push(imgSrc);
      }
    }
  }

  // Extract vendor/brand
  let vendor = 'Unknown Vendor';
  const vendorPatterns = [
    /<meta[^>]*property=["']product:brand["'][^>]*content=["']([^"']*)["']/i,
    /["']vendor["']\s*:\s*["']([^"']*)["']/i,
    /brand[^>]*>([^<]*)</i
  ];

  for (const pattern of vendorPatterns) {
    const match = html.match(pattern);
    if (match && match[1].trim()) {
      vendor = match[1].trim();
      break;
    }
  }

  return {
    handle: handle,
    title: title,
    description: description || 'Product description not available.',
    vendor: vendor,
    type: 'General',
    tags: [],
    available: true,
    images: images.slice(0, 10), // Limit to first 10 images
    price: price,
    compare_at_price: null,
    variants: [{
      id: Date.now(),
      title: 'Default Title',
      price: price,
      available: true,
      sku: handle.toUpperCase(),
      inventory_quantity: 1,
      weight: 0
    }]
  };
}

// Simulate API delay
export function simulateApiDelay(ms: number = 2000): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}