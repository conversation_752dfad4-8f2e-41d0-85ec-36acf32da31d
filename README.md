# Shopify Scraper Wizard

A powerful web application for extracting and converting Shopify product data into CSV format for easy import/export operations.

## ✨ Features

### Single Product Scraping
- Extract detailed product information from any Shopify product URL
- Support for all product variants, options, and pricing
- High-quality image extraction
- Complete metadata including SEO, tags, and descriptions

### Store-Wide Discovery
- **NEW**: Discover and list all products from any Shopify store
- Bulk product selection with intuitive checkboxes
- "Select All" or choose specific products
- Export multiple products in a single CSV file

### Enhanced Data Accuracy
- **FIXED**: Correct currency detection and display (€, £, $, ¥, etc.)
- Smart price conversion handling different formats
- Accurate compare-at-price (MSRP) extraction
- Proper variant and option handling

### Export Features
- Shopify-ready CSV format for direct import
- Complete product data including variants, images, and metadata
- Bulk export capabilities for multiple products
- Optimized file structure for large catalogs

## 🚀 Quick Start

### Development
```bash
npm install
npm run dev
```

### Production Build
```bash
npm run build
npm run preview
```

## 📱 Usage

### Single Product Mode
1. Navigate to the home page
2. Enter a Shopify product URL
3. Click "Extract Product Data"
4. Download the generated CSV file

### Store Discovery Mode
1. Click "Store Discovery" in the navigation or the CTA on the home page
2. Enter a Shopify store URL (e.g., `https://store-name.myshopify.com`)
3. Click "Discover Products" to scan the entire store
4. Select individual products or use "Select All"
5. Choose "Download All" or "Download Selected"

## 🛠 Technical Details

**URL**: https://lovable.dev/projects/6cb8cdd8-7e3b-4a0d-9d5f-ee38eef57fad

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/6cb8cdd8-7e3b-4a0d-9d5f-ee38eef57fad) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## 🚀 Deployment

### Quick Deploy with Lovable
Simply open [Lovable](https://lovable.dev/projects/6cb8cdd8-7e3b-4a0d-9d5f-ee38eef57fad) and click on Share -> Publish.

### Manual Deployment
See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed deployment instructions for various hosting platforms including Netlify, Vercel, GitHub Pages, and more.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
