Stack trace:
Frame         Function      Args
0007FFFFBE30  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFAD30) msys-2.0.dll+0x1FEBA
0007FFFFBE30  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC108) msys-2.0.dll+0x67F9
0007FFFFBE30  000210046832 (000210285FF9, 0007FFFFBCE8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE30  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBE30  0002100690B4 (0007FFFFBE40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFC110  00021006A49D (0007FFFFBE40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF9E3760000 ntdll.dll
7FF9E1BF0000 KERNEL32.DLL
7FF9E0F10000 KERNELBASE.dll
7FF9DAB30000 apphelp.dll
7FF9E2830000 USER32.dll
7FF9E09F0000 win32u.dll
000210040000 msys-2.0.dll
7FF9E1FF0000 GDI32.dll
7FF9E08B0000 gdi32full.dll
7FF9E13C0000 msvcp_win.dll
7FF9E0C40000 ucrtbase.dll
7FF9E1600000 advapi32.dll
7FF9E2CF0000 msvcrt.dll
7FF9E16C0000 sechost.dll
7FF9E17A0000 RPCRT4.dll
7FF9DFFB0000 CRYPTBASE.DLL
7FF9E0A20000 bcryptPrimitives.dll
7FF9E15C0000 IMM32.DLL
