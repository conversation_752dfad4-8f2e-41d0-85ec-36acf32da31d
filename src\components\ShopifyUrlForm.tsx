import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Search, Wand2, ExternalLink, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ShopifyUrlFormProps {
  onSubmit: (url: string) => void;
  isLoading: boolean;
}

export function ShopifyUrlForm({ onSubmit, isLoading }: ShopifyUrlFormProps) {
  const [url, setUrl] = useState("");
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const { toast } = useToast();

  const validateShopifyUrl = (inputUrl: string): boolean => {
    if (!inputUrl) return false;
    
    try {
      const urlObj = new URL(inputUrl);
      const hostname = urlObj.hostname.toLowerCase();
      const pathname = urlObj.pathname.toLowerCase();
      
      // Check if it has /products/ in the path (any domain extension is valid for Shopify stores)
      const hasProducts = pathname.includes('/products/');

      // Basic domain validation - must have at least one dot and valid TLD
      const hasValidDomain = hostname.includes('.') && hostname.split('.').length >= 2;

      return hasValidDomain && hasProducts;
    } catch {
      return false;
    }
  };

  const handleUrlChange = (value: string) => {
    setUrl(value);
    if (value) {
      const valid = validateShopifyUrl(value);
      setIsValid(valid);
    } else {
      setIsValid(null);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!url.trim()) {
      toast({
        title: "URL Required",
        description: "Please enter a Shopify product URL",
        variant: "destructive",
      });
      return;
    }

    if (!validateShopifyUrl(url)) {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid Shopify product URL (e.g., https://store.com/products/product-name)",
        variant: "destructive",
      });
      return;
    }

    onSubmit(url);
  };

  return (
    <Card className="w-full max-w-2xl mx-auto bg-gradient-card border-border/50 shadow-card">
      <CardHeader className="text-center pb-4">
        <CardTitle className="text-2xl font-bold bg-gradient-primary bg-clip-text text-transparent">
          Shopify Product Scraper
        </CardTitle>
        <p className="text-muted-foreground">
          Enter a Shopify product URL to extract and download product data as CSV
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="url" className="text-sm font-medium text-foreground">
              Shopify Product URL
            </label>
            <div className="relative">
              <Input
                id="url"
                type="url"
                placeholder="https://example.com/products/product-name"
                value={url}
                onChange={(e) => handleUrlChange(e.target.value)}
                className={`pl-10 pr-10 transition-all duration-200 ${
                  isValid === true ? "border-success" : 
                  isValid === false ? "border-destructive" : ""
                }`}
                disabled={isLoading}
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              {isValid === true && (
                <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-success" />
              )}
            </div>
          </div>

          {isValid === false && url && (
            <Alert variant="destructive">
              <AlertDescription>
                Please enter a valid Shopify product URL. It should contain "/products/" in the path.
              </AlertDescription>
            </Alert>
          )}

          <Button
            type="submit"
            variant="wizard"
            size="lg"
            className="w-full"
            disabled={isLoading || !url || isValid === false}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Extracting Product Data...
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4" />
                Extract Product Data
              </>
            )}
          </Button>
        </form>

        <div className="bg-muted/30 rounded-lg p-4 space-y-2">
          <h4 className="text-sm font-medium text-foreground">Example URLs:</h4>
          <div className="space-y-1 text-xs">
            <button
              type="button"
              onClick={() => handleUrlChange("https://olympuslondon.com/products/amy")}
              className="flex items-center gap-2 text-accent hover:text-accent/80 transition-colors"
              disabled={isLoading}
            >
              <ExternalLink className="h-3 w-3" />
              https://olympuslondon.com/products/amy
            </button>
            <button
              type="button"
              onClick={() => handleUrlChange("https://example.myshopify.com/products/sample-product")}
              className="flex items-center gap-2 text-accent hover:text-accent/80 transition-colors"
              disabled={isLoading}
            >
              <ExternalLink className="h-3 w-3" />
              https://example.myshopify.com/products/sample-product
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}