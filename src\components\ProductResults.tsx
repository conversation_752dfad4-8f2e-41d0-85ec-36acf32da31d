import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Download, Package, DollarSign, Image, Tag, FileText } from "lucide-react";
import <PERSON> from "papaparse";

interface ProductVariant {
  id: string;
  title: string;
  price: string;
  compare_at_price?: string;
  sku?: string;
  inventory_quantity?: number;
  weight?: number;
  option1?: string;
  option2?: string;
  option3?: string;
}

interface ProductData {
  handle: string;
  title: string;
  body_html: string;
  vendor: string;
  type: string;
  tags: string[];
  published: boolean;
  images: string[];
  variants: ProductVariant[];
  price: string;
  compare_at_price?: string;
  currency?: string;
  currency_symbol?: string;
}

interface ProductResultsProps {
  productData: ProductData;
  onReset: () => void;
}

export function ProductResults({ productData, onReset }: ProductResultsProps) {
  const currencySymbol = productData.currency_symbol || '$';

  const generateCSV = () => {
    const csvData = [];
    
    // For each variant, create a row
    productData.variants.forEach((variant, index) => {
      const row = {
        Handle: productData.handle,
        Title: index === 0 ? productData.title : "", // Only show title on first row
        "Body (HTML)": index === 0 ? productData.body_html : "",
        Vendor: index === 0 ? productData.vendor : "",
        Type: index === 0 ? productData.type : "",
        Tags: index === 0 ? productData.tags.join(", ") : "",
        Published: index === 0 ? productData.published : "",
        "Option1 Name": variant.option1 ? "Title" : "",
        "Option1 Value": variant.option1 || "",
        "Option2 Name": variant.option2 ? "Size" : "",
        "Option2 Value": variant.option2 || "",
        "Option3 Name": variant.option3 ? "Color" : "",
        "Option3 Value": variant.option3 || "",
        "Variant SKU": variant.sku || "",
        "Variant Grams": variant.weight || "",
        "Variant Inventory Tracker": "shopify",
        "Variant Inventory Qty": variant.inventory_quantity || "",
        "Variant Inventory Policy": "deny",
        "Variant Fulfillment Service": "manual",
        "Variant Price": variant.price,
        "Variant Compare At Price": variant.compare_at_price || "",
        "Variant Requires Shipping": "TRUE",
        "Variant Taxable": "TRUE",
        "Variant Barcode": "",
        "Image Src": index === 0 ? productData.images[0] || "" : "",
        "Image Position": index === 0 ? "1" : "",
        "Image Alt Text": index === 0 ? productData.title : "",
        "Gift Card": "FALSE",
        "SEO Title": index === 0 ? productData.title : "",
        "SEO Description": index === 0 ? productData.body_html.replace(/<[^>]*>/g, "").substring(0, 160) : "",
        "Google Shopping / Google Product Category": "",
        "Google Shopping / Gender": "",
        "Google Shopping / Age Group": "",
        "Google Shopping / MPN": "",
        "Google Shopping / AdWords Grouping": "",
        "Google Shopping / AdWords Labels": "",
        "Google Shopping / Condition": "",
        "Google Shopping / Custom Product": "",
        "Google Shopping / Custom Label 0": "",
        "Google Shopping / Custom Label 1": "",
        "Google Shopping / Custom Label 2": "",
        "Google Shopping / Custom Label 3": "",
        "Google Shopping / Custom Label 4": "",
        "Variant Image": "",
        "Variant Weight Unit": "g",
        "Variant Tax Code": "",
        "Cost per item": "",
        "Status": "active"
      };
      csvData.push(row);
    });

    const csv = Papa.unparse(csvData);
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", `${productData.handle}-shopify-import.csv`);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6 animate-fade-in">
      {/* Product Overview */}
      <Card className="bg-gradient-card border-border/50 shadow-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5 text-primary" />
            Product Overview
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold text-lg text-foreground">{productData.title}</h3>
              <p className="text-muted-foreground">Handle: {productData.handle}</p>
              <p className="text-muted-foreground">Vendor: {productData.vendor}</p>
              <p className="text-muted-foreground">Type: {productData.type}</p>
              <Badge variant={productData.published ? "default" : "secondary"} className="mt-2">
                {productData.published ? "Published" : "Draft"}
              </Badge>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-success" />
                <span className="font-semibold text-success">{currencySymbol}{productData.price}</span>
                {productData.compare_at_price && (
                  <span className="text-muted-foreground line-through">{currencySymbol}{productData.compare_at_price}</span>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Image className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {productData.images.length} image(s)
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {productData.variants.length} variant(s)
                </span>
              </div>
            </div>
          </div>

          {productData.tags.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm font-medium text-foreground">Tags:</p>
              <div className="flex flex-wrap gap-1">
                {productData.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {productData.body_html && (
            <div className="space-y-2">
              <p className="text-sm font-medium text-foreground">Description:</p>
              <div 
                className="text-sm text-muted-foreground bg-muted/30 p-3 rounded max-h-32 overflow-y-auto"
                dangerouslySetInnerHTML={{ 
                  __html: productData.body_html.length > 200 
                    ? productData.body_html.substring(0, 200) + "..." 
                    : productData.body_html 
                }}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Variants */}
      {productData.variants.length > 0 && (
        <Card className="bg-gradient-card border-border/50 shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5 text-primary" />
              Product Variants ({productData.variants.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {productData.variants.map((variant, index) => (
                <div 
                  key={variant.id} 
                  className="flex items-center justify-between p-3 bg-muted/20 rounded-lg border border-border/30"
                >
                  <div className="space-y-1">
                    <p className="font-medium text-foreground">{variant.title}</p>
                    <div className="text-xs text-muted-foreground space-x-4">
                      {variant.sku && <span>SKU: {variant.sku}</span>}
                      {variant.weight && <span>Weight: {variant.weight}g</span>}
                      {variant.inventory_quantity !== undefined && (
                        <span>Stock: {variant.inventory_quantity}</span>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-2 justify-end">
                      <p className="font-semibold text-success">{currencySymbol}{variant.price}</p>
                      {variant.compare_at_price && (
                        <p className="text-sm text-muted-foreground line-through">{currencySymbol}{variant.compare_at_price}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Actions */}
      <Card className="bg-gradient-card border-border/50 shadow-card">
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={generateCSV}
              variant="wizard"
              size="lg"
              className="flex-1"
            >
              <Download className="h-4 w-4" />
              Download Shopify CSV
            </Button>
            <Button
              onClick={onReset}
              variant="outline"
              size="lg"
              className="flex-1 sm:flex-none"
            >
              <FileText className="h-4 w-4" />
              Scrape Another Product
            </Button>
          </div>
          <p className="text-xs text-muted-foreground mt-3 text-center">
            CSV file will be formatted for direct import into Shopify admin
          </p>
        </CardContent>
      </Card>
    </div>
  );
}