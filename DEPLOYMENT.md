# Deployment Guide

This guide explains how to deploy the Shopify Scraper Wizard application to various hosting platforms.

## Build for Production

```bash
npm run build
```

This creates a `dist` folder with the production-ready files.

## Hosting Platforms

### Netlify

1. Connect your repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `dist`
4. The `_redirects` file in the `public` folder will handle client-side routing automatically

### Vercel

1. Connect your repository to Vercel
2. Vercel automatically detects Vite projects
3. Client-side routing is handled automatically

### GitHub Pages

1. Build the project: `npm run build`
2. Deploy the `dist` folder to GitHub Pages
3. If using a custom domain, ensure the routing works with the `.htaccess` file

### Apache Server

1. Upload the contents of the `dist` folder to your web server
2. The `.htaccess` file in the `public` folder will handle client-side routing
3. Ensure mod_rewrite is enabled on your Apache server

### Nginx

Add this configuration to your Nginx server block:

```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

### Other Static Hosting

For other static hosting services:
1. Upload the contents of the `dist` folder
2. Configure the server to serve `index.html` for all routes that don't match files
3. Ensure CORS is properly configured if needed

## Environment Considerations

### CORS Issues

The application uses CORS proxies for accessing external Shopify stores. In production:

1. The app will first try direct requests to Shopify stores
2. If CORS blocks the request, it falls back to using `https://api.allorigins.win/raw?url=`
3. For better reliability, consider setting up your own CORS proxy

### Performance

The production build includes:
- Code splitting for better loading performance
- Optimized chunks for vendor libraries and routing
- Compressed assets

### Security

- All external requests are made client-side
- No sensitive data is stored or transmitted through your server
- The application works entirely in the browser

## Testing Production Build Locally

```bash
npm run preview
```

This serves the production build locally for testing.

## Troubleshooting

### Routes Not Working

If direct navigation to routes like `/store-discovery` returns 404:
- Ensure your hosting platform supports client-side routing
- Check that the `_redirects` or `.htaccess` file is properly deployed
- Configure your server to serve `index.html` for all routes

### CORS Issues

If you encounter CORS issues with certain Shopify stores:
- This is expected behavior for some stores
- The app automatically falls back to CORS proxy
- Consider implementing your own CORS proxy for better reliability

### Build Issues

If the build fails:
- Ensure all dependencies are installed: `npm install`
- Check for TypeScript errors: `npm run type-check` (if available)
- Update dependencies if needed: `npm update`
