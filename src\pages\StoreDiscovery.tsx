import { useState } from "react";
import { Header } from "@/components/Header";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { LoadingState } from "@/components/LoadingState";
import { useToast } from "@/hooks/use-toast";
import { Store, Search, Download, Package, DollarSign, CheckSquare, Square } from "lucide-react";
import { discoverStoreProducts } from "@/utils/storeDiscovery";
import { extractProductData } from "@/utils/simpleProductData";
import Papa from "papaparse";

interface DiscoveredProduct {
  handle: string;
  title: string;
  price: string;
  compare_at_price?: string;
  vendor: string;
  type: string;
  url: string;
  image?: string;
  available: boolean;
  currency?: string;
  currency_symbol?: string;
}

type AppState = 'idle' | 'discovering' | 'results' | 'exporting' | 'error';

const StoreDiscovery = () => {
  const [state, setState] = useState<AppState>('idle');
  const [storeUrl, setStoreUrl] = useState('');
  const [discoveredProducts, setDiscoveredProducts] = useState<DiscoveredProduct[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set());
  const [isSelectAll, setIsSelectAll] = useState(false);
  const { toast } = useToast();

  const handleStoreUrlSubmit = async () => {
    if (!storeUrl.trim()) {
      toast({
        title: "Error",
        description: "Please enter a valid Shopify store URL",
        variant: "destructive",
      });
      return;
    }

    setState('discovering');

    try {
      console.log('🚀 Starting store product discovery for:', storeUrl);

      // Use the real store discovery logic
      const discoveredProducts = await discoverStoreProducts(storeUrl);

      console.log(`✅ Successfully discovered ${discoveredProducts.length} products`);

      setDiscoveredProducts(discoveredProducts);
      setState('results');

      toast({
        title: "Success!",
        description: `Discovered ${discoveredProducts.length} products from the store`,
        variant: "default",
      });
      
    } catch (error) {
      console.error('Store discovery error:', error);
      setState('error');
      
      toast({
        title: "Error",
        description: "Failed to discover products from the store. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleProductSelection = (productHandle: string, checked: boolean) => {
    const newSelected = new Set(selectedProducts);
    if (checked) {
      newSelected.add(productHandle);
    } else {
      newSelected.delete(productHandle);
    }
    setSelectedProducts(newSelected);
    setIsSelectAll(newSelected.size === discoveredProducts.length);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(new Set(discoveredProducts.map(p => p.handle)));
    } else {
      setSelectedProducts(new Set());
    }
    setIsSelectAll(checked);
  };

  const handleDownloadSelected = async () => {
    if (selectedProducts.size === 0) {
      toast({
        title: "No Products Selected",
        description: "Please select at least one product to download",
        variant: "destructive",
      });
      return;
    }

    setState('exporting');

    try {
      console.log(`🚀 Starting detailed extraction for ${selectedProducts.size} selected products`);

      const selectedProductData = discoveredProducts.filter(p => selectedProducts.has(p.handle));
      const detailedProducts = [];

      // Extract detailed data for each selected product
      for (let i = 0; i < selectedProductData.length; i++) {
        const product = selectedProductData[i];
        console.log(`📦 Extracting detailed data for product ${i + 1}/${selectedProductData.length}: ${product.title}`);

        try {
          // Use the existing extractProductData function to get full product details
          const detailedData = await extractProductData(product.url);
          detailedProducts.push(detailedData);

          // Add a small delay between requests to be respectful
          if (i < selectedProductData.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        } catch (error) {
          console.warn(`❌ Failed to extract detailed data for ${product.title}:`, error);

          // Fallback to basic data from discovery
          detailedProducts.push({
            handle: product.handle,
            title: product.title,
            body_html: "<p>Product description not available</p>",
            vendor: product.vendor,
            type: product.type,
            tags: [],
            published: product.available,
            images: product.image ? [product.image] : [],
            variants: [{
              id: 'default-var',
              title: 'Default Title',
              price: product.price,
              compare_at_price: product.compare_at_price,
              sku: product.handle.toUpperCase(),
              inventory_quantity: 1,
              weight: 0
            }],
            price: product.price,
            compare_at_price: product.compare_at_price
          });
        }
      }

      // Generate CSV using the same logic as ProductResults component
      const csvData: any[] = [];

      detailedProducts.forEach((productData) => {
        productData.variants.forEach((variant: any, index: number) => {
          const row = {
            Handle: productData.handle,
            Title: index === 0 ? productData.title : "",
            "Body (HTML)": index === 0 ? productData.body_html : "",
            Vendor: index === 0 ? productData.vendor : "",
            Type: index === 0 ? productData.type : "",
            Tags: index === 0 ? (Array.isArray(productData.tags) ? productData.tags.join(", ") : "") : "",
            Published: index === 0 ? productData.published : "",
            "Option1 Name": variant.option1 ? "Title" : "",
            "Option1 Value": variant.option1 || "",
            "Option2 Name": variant.option2 ? "Size" : "",
            "Option2 Value": variant.option2 || "",
            "Option3 Name": variant.option3 ? "Color" : "",
            "Option3 Value": variant.option3 || "",
            "Variant SKU": variant.sku || "",
            "Variant Grams": variant.weight || "",
            "Variant Inventory Tracker": "shopify",
            "Variant Inventory Qty": variant.inventory_quantity || "",
            "Variant Inventory Policy": "deny",
            "Variant Fulfillment Service": "manual",
            "Variant Price": variant.price,
            "Variant Compare At Price": variant.compare_at_price || "",
            "Variant Requires Shipping": "TRUE",
            "Variant Taxable": "TRUE",
            "Variant Barcode": "",
            "Image Src": index === 0 ? (productData.images?.[0] || "") : "",
            "Image Position": index === 0 ? "1" : "",
            "Image Alt Text": index === 0 ? productData.title : "",
            "Gift Card": "FALSE",
            "SEO Title": index === 0 ? productData.title : "",
            "SEO Description": index === 0 ? (productData.body_html ? productData.body_html.replace(/<[^>]*>/g, "").substring(0, 160) : "") : "",
            "Google Shopping / Google Product Category": "",
            "Google Shopping / Gender": "",
            "Google Shopping / Age Group": "",
            "Google Shopping / MPN": "",
            "Google Shopping / AdWords Grouping": "",
            "Google Shopping / AdWords Labels": "",
            "Google Shopping / Condition": "",
            "Google Shopping / Custom Product": "",
            "Google Shopping / Custom Label 0": "",
            "Google Shopping / Custom Label 1": "",
            "Google Shopping / Custom Label 2": "",
            "Google Shopping / Custom Label 3": "",
            "Google Shopping / Custom Label 4": "",
            "Variant Image": "",
            "Variant Weight Unit": "g",
            "Variant Tax Code": "",
            "Cost per item": "",
            "Status": "active"
          };
          csvData.push(row);
        });
      });

      const csv = Papa.unparse(csvData);
      const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");

      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", `store-products-${Date.now()}.csv`);
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      setState('results');

      toast({
        title: "Export Complete!",
        description: `Successfully exported ${selectedProducts.size} products with full details to CSV`,
        variant: "default",
      });

    } catch (error) {
      console.error('Export error:', error);
      setState('results');

      toast({
        title: "Export Failed",
        description: "Failed to export products. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDownloadAll = async () => {
    setSelectedProducts(new Set(discoveredProducts.map(p => p.handle)));
    setIsSelectAll(true);
    await handleDownloadSelected();
  };

  const handleReset = () => {
    setState('idle');
    setStoreUrl('');
    setDiscoveredProducts([]);
    setSelectedProducts(new Set());
    setIsSelectAll(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {state === 'idle' && (
          <div className="space-y-8 animate-fade-in">
            {/* Hero Section */}
            <div className="text-center space-y-6 py-8">
              <div className="relative inline-block">
                <Store className="w-24 h-24 text-primary mx-auto" />
              </div>
              <div className="space-y-3">
                <h1 className="text-4xl md:text-5xl font-bold bg-gradient-primary bg-clip-text text-transparent">
                  Store Product Discovery
                </h1>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Discover and export all products from any Shopify store. 
                  Choose to download everything or select specific products.
                </p>
              </div>
            </div>

            {/* Store URL Input */}
            <Card className="max-w-2xl mx-auto bg-gradient-card border-border/50 shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5 text-primary" />
                  Enter Store URL
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Input
                    type="url"
                    placeholder="https://store-name.myshopify.com"
                    value={storeUrl}
                    onChange={(e) => setStoreUrl(e.target.value)}
                    className="text-lg"
                  />
                  <p className="text-sm text-muted-foreground">
                    Enter the main URL of any Shopify store to discover all available products
                  </p>
                </div>
                <Button
                  onClick={handleStoreUrlSubmit}
                  variant="wizard"
                  size="lg"
                  className="w-full"
                  disabled={!storeUrl.trim()}
                >
                  <Search className="h-4 w-4" />
                  Discover Products
                </Button>
              </CardContent>
            </Card>
          </div>
        )}

        {state === 'discovering' && (
          <LoadingState stage="fetching" />
        )}

        {state === 'results' && discoveredProducts.length > 0 && (
          <div className="space-y-6 animate-fade-in">
            {/* Results Header */}
            <Card className="bg-gradient-card border-border/50 shadow-card">
              <CardContent className="pt-6">
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  <div>
                    <h2 className="text-2xl font-bold text-foreground">
                      Discovered {discoveredProducts.length} Products
                    </h2>
                    <p className="text-muted-foreground">
                      {selectedProducts.size} selected for export
                    </p>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button
                      onClick={handleDownloadAll}
                      variant="wizard"
                      disabled={state === 'exporting'}
                    >
                      <Download className="h-4 w-4" />
                      Download All
                    </Button>
                    <Button
                      onClick={handleDownloadSelected}
                      variant="outline"
                      disabled={selectedProducts.size === 0 || state === 'exporting'}
                    >
                      <Download className="h-4 w-4" />
                      Download Selected ({selectedProducts.size})
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Select All */}
            <Card className="bg-gradient-card border-border/50 shadow-card">
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="select-all"
                    checked={isSelectAll}
                    onCheckedChange={handleSelectAll}
                  />
                  <label htmlFor="select-all" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Select All Products
                  </label>
                </div>
              </CardContent>
            </Card>

            {/* Product Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {discoveredProducts.map((product) => (
                <Card key={product.handle} className="bg-gradient-card border-border/50 shadow-card">
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      <Checkbox
                        id={product.handle}
                        checked={selectedProducts.has(product.handle)}
                        onCheckedChange={(checked) => handleProductSelection(product.handle, checked as boolean)}
                      />
                      <div className="flex-1 space-y-2">
                        {product.image && (
                          <img
                            src={product.image}
                            alt={product.title}
                            className="w-full h-32 object-cover rounded"
                          />
                        )}
                        <div>
                          <h3 className="font-semibold text-sm text-foreground line-clamp-2">
                            {product.title}
                          </h3>
                          <p className="text-xs text-muted-foreground">{product.vendor}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="font-semibold text-success">
                              {product.currency_symbol || '$'}{product.price}
                            </span>
                            {product.compare_at_price && (
                              <span className="text-xs text-muted-foreground line-through">
                                {product.currency_symbol || '$'}{product.compare_at_price}
                              </span>
                            )}
                          </div>
                          <Badge variant={product.available ? "default" : "secondary"} className="text-xs mt-1">
                            {product.available ? "Available" : "Unavailable"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Reset Button */}
            <div className="text-center">
              <Button onClick={handleReset} variant="outline">
                Discover Another Store
              </Button>
            </div>
          </div>
        )}

        {state === 'exporting' && (
          <LoadingState stage="generating" />
        )}

        {state === 'error' && (
          <div className="text-center py-16 animate-fade-in">
            <div className="space-y-4">
              <div className="w-16 h-16 bg-destructive/20 rounded-full flex items-center justify-center mx-auto">
                <svg className="h-8 w-8 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-foreground">Something went wrong</h2>
              <p className="text-muted-foreground">
                We couldn't discover products from the store. Please check the URL and try again.
              </p>
              <Button onClick={handleReset} className="mt-4">
                Try Again
              </Button>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default StoreDiscovery;
