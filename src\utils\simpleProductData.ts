// Simple product data extractor using the exact same logic as test-cors.html

interface ProductVariant {
  id: string;
  title: string;
  price: string;
  sku?: string;
  inventory_quantity?: number;
  weight?: number;
  option1?: string;
  option2?: string;
  option3?: string;
}

interface ProductData {
  handle: string;
  title: string;
  body_html: string;
  vendor: string;
  type: string;
  tags: string[];
  published: boolean;
  images: string[];
  variants: ProductVariant[];
  price: string;
  compare_at_price?: string;
}

const CORS_PROXY = 'https://api.allorigins.win/raw?url=';

export async function extractProductData(url: string): Promise<ProductData> {
  console.log('🚀 Starting extraction with test-cors.html logic for:', url);
  
  try {
    // Extract handle (exact same as test-cors.html)
    const urlParts = new URL(url);
    const pathParts = urlParts.pathname.split('/');
    const handle = pathParts[pathParts.indexOf('products') + 1];
    
    console.log('Testing URL:', url);
    console.log('Handle:', handle);
    
    // Test direct fetch first (exact same as test-cors.html)
    let productData = null;
    let method = 'unknown';
    
    try {
      const directUrl = `${urlParts.origin}/products/${handle}.js`;
      console.log('Trying direct fetch:', directUrl);
      
      const response = await fetch(directUrl);
      if (response.ok) {
        productData = await response.json();
        method = 'direct';
        console.log('Direct fetch successful');
      } else {
        throw new Error(`Direct fetch failed: ${response.status}`);
      }
    } catch (directError) {
      console.log('Direct fetch failed:', directError.message);
      
      // Try CORS proxy (exact same as test-cors.html)
      try {
        const proxyUrl = `${CORS_PROXY}${encodeURIComponent(urlParts.origin + '/products/' + handle + '.js')}`;
        console.log('Trying CORS proxy:', proxyUrl);
        
        const proxyResponse = await fetch(proxyUrl);
        if (proxyResponse.ok) {
          productData = await proxyResponse.json();
          method = 'proxy';
          console.log('CORS proxy successful');
        } else {
          throw new Error(`CORS proxy failed: ${proxyResponse.status}`);
        }
      } catch (proxyError) {
        throw new Error(`Both direct and proxy methods failed. Direct: ${directError.message}, Proxy: ${proxyError.message}`);
      }
    }
    
    if (productData) {
      console.log('✅ Success (' + method + ')');
      console.log('Title:', productData.title);
      console.log('🔍 RAW PRICE DATA FROM SHOPIFY:', {
        productPrice: productData.price,
        productCompareAtPrice: productData.compare_at_price,
        currency: productData.currency || 'Unknown',
        priceMin: productData.price_min,
        priceMax: productData.price_max,
        firstVariantPrice: productData.variants?.[0]?.price,
        firstVariantCompareAtPrice: productData.variants?.[0]?.compare_at_price,
        allVariantPrices: productData.variants?.slice(0, 3).map(v => ({
          title: v.title,
          price: v.price,
          compare_at_price: v.compare_at_price
        }))
      });
      console.log('Vendor:', productData.vendor);

      // Process price and compare_at_price FIRST (before variants)
      // Auto-detect the correct price divisor based on the price magnitude
      const detectPriceDivisor = (price: number, expectedRange: [number, number] = [1, 1000]) => {
        if (price >= 10000) return 1000; // Prices in thousandths (e.g., 34950 = €34.95)
        if (price >= 100) return 100;   // Prices in hundredths (e.g., 3495 = €34.95)
        return 1;                       // Prices already in currency units
      };

      const priceDivisor = typeof productData.price === 'number' ?
        detectPriceDivisor(productData.price) : 100;

      // Convert to our format using the detected price divisor
      const variants = productData.variants?.map((variant: any, index: number) => ({
        id: variant.id?.toString() || `var-${index}`,
        title: variant.title || 'Default Title',
        price: typeof variant.price === 'number' ? (variant.price / priceDivisor).toFixed(2) :
               typeof variant.price === 'string' ? variant.price : '0.00',
        compare_at_price: variant.compare_at_price && typeof variant.compare_at_price === 'number' ?
          (variant.compare_at_price / priceDivisor).toFixed(2) : undefined,
        sku: variant.sku || '',
        inventory_quantity: variant.inventory_quantity || 0,
        weight: variant.weight || 0,
        option1: variant.option1 || null,
        option2: variant.option2 || null,
        option3: variant.option3 || null
      })) || [];

      // Process images
      const images = productData.images?.map((img: string) => {
        let imageUrl = img;
        if (imageUrl.startsWith('//')) {
          imageUrl = `https:${imageUrl}`;
        } else if (imageUrl.startsWith('/')) {
          imageUrl = `${urlParts.origin}${imageUrl}`;
        }
        return imageUrl;
      }) || [];

      console.log('🔢 Price divisor detection:', {
        rawPrice: productData.price,
        detectedDivisor: priceDivisor,
        convertedPrice: typeof productData.price === 'number' ? (productData.price / priceDivisor).toFixed(2) : 'N/A'
      });

      const mainPrice = typeof productData.price === 'number' ?
        (productData.price / priceDivisor).toFixed(2) :
        variants.length > 0 ? variants[0].price : '0.00';

      // Extract compare_at_price (original/crossed-out price)
      const compareAtPrice = productData.compare_at_price && typeof productData.compare_at_price === 'number' ?
        (productData.compare_at_price / priceDivisor).toFixed(2) :
        // Also check variants for compare_at_price
        variants.length > 0 && variants[0].compare_at_price ?
          (typeof variants[0].compare_at_price === 'number' ?
            (variants[0].compare_at_price / priceDivisor).toFixed(2) :
            variants[0].compare_at_price) :
          undefined;

      console.log('💰 FINAL PRICE CONVERSION:', {
        'Raw product price': productData.price,
        'Raw product compare_at_price': productData.compare_at_price,
        'Converted main price': mainPrice,
        'Converted compare_at_price': compareAtPrice,
        'Price conversion logic': typeof productData.price === 'number' ? 'Divided by 100 (cents to dollars)' : 'Used as string',
        'Compare price conversion logic': productData.compare_at_price ? 'Divided by 100 (cents to dollars)' : 'No compare price available'
      });
      
      return {
        handle: productData.handle || handle,
        title: productData.title || `Product ${handle}`,
        body_html: productData.description || '<p>No description available.</p>',
        vendor: productData.vendor || 'Unknown Vendor',
        type: productData.type || 'General',
        tags: Array.isArray(productData.tags) ? productData.tags : [],
        published: productData.available !== false,
        images: images,
        variants: variants.length > 0 ? variants : [{
          id: 'default-var',
          title: 'Default Title',
          price: mainPrice,
          sku: handle.toUpperCase(),
          inventory_quantity: 1,
          weight: 0
        }],
        price: mainPrice,
        compare_at_price: compareAtPrice
      };
    } else {
      throw new Error('No product data received');
    }
    
  } catch (error) {
    console.error('❌ Failed:', error);
    throw error;
  }
}
