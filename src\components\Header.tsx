import { Wand2, Github, ExternalLink, Package, Store } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useLocation, Link } from "react-router-dom";

export function Header() {
  const location = useLocation();

  return (
    <header className="border-b border-border/50 bg-background/80 backdrop-blur-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Link to="/" className="flex items-center gap-3">
              <div className="p-2 bg-gradient-primary rounded-lg shadow-glow">
                <Wand2 className="h-6 w-6 text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
                  Shopify Scraper Wizard
                </h1>
                <p className="text-xs text-muted-foreground">
                  Extract & Convert Product Data
                </p>
              </div>
            </Link>
          </div>

          {/* Navigation */}
          <div className="flex items-center gap-2">
            <Button
              variant={location.pathname === "/" ? "default" : "ghost"}
              size="sm"
              asChild
            >
              <Link to="/" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                <span className="hidden sm:inline">Single Product</span>
              </Link>
            </Button>
            <Button
              variant={location.pathname === "/store-discovery" ? "default" : "ghost"}
              size="sm"
              asChild
            >
              <Link to="/store-discovery" className="flex items-center gap-2">
                <Store className="h-4 w-4" />
                <span className="hidden sm:inline">Store Discovery</span>
              </Link>
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" asChild>
              <a 
                href="https://github.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-2"
              >
                <Github className="h-4 w-4" />
                <span className="hidden sm:inline">GitHub</span>
              </a>
            </Button>
            <Button variant="ghost" size="sm" asChild>
              <a 
                href="https://help.shopify.com/en/manual/products/import-export/import-products" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-2"
              >
                <ExternalLink className="h-4 w-4" />
                <span className="hidden sm:inline">Shopify Docs</span>
              </a>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}